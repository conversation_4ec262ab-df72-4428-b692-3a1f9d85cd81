import { useState, useCallback } from 'react';

/**
 * <PERSON> HTTP 聊天消息类型定义
 */
interface MarioChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  createdAt: string;
}

/**
 * <PERSON> HTTP Hook 的返回类型
 */
interface UseMarioHttpReturn {
  messages: MarioChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  isConnected: boolean;
}

/**
 * Mario HTTP Hook 配置选项
 */
interface UseMarioHttpOptions {
  agentId: string;
  sessionId: string;
  onCanvasOpen?: () => void;
  onTestCaseOpen?: () => void;
}

/**
 * <PERSON> HTTP Hook
 * 用于处理与 Mario Agent 的 HTTP API 交互
 * 提供与 WebSocket hook 相同的接口，但使用 HTTP 请求
 */
export function useMarioHttp({
  agentId,
  sessionId,
  onCanvasOpen,
  onTestCaseOpen
}: UseMarioHttpOptions): UseMarioHttpReturn {
  const [messages, setMessages] = useState<MarioChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 创建新的消息对象
   */
  const createMessage = useCallback((content: string, role: 'user' | 'assistant' = 'assistant'): MarioChatMessage => {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      role,
      createdAt: new Date().toISOString()
    };
  }, []);

  /**
   * 发送消息到 Mario Agent
   */
  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;

      // 添加用户消息到消息列表
      const userMessage = createMessage(content, 'user');
      setMessages((prev) => [...prev, userMessage]);

      // 设置加载状态
      setIsLoading(true);
      setError(null);

      try {
        // 发送请求到 Mario API
        const response = await fetch(`/api/integration/mario/${agentId}?sessionId=${sessionId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            message: content,
            messages: messages.map((msg) => ({
              role: msg.role,
              content: msg.content,
              timestamp: msg.createdAt
            })),
            agentId,
            sessionId,
            timestamp: Date.now()
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.details || errorData.error || `HTTP ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.response) {
          // 添加助手回复到消息列表
          const assistantMessage = createMessage(data.response.content, 'assistant');
          setMessages((prev) => [...prev, assistantMessage]);

          // 处理特殊事件
          if (data.response.content.includes('canvas_open')) {
            onCanvasOpen?.();
            window.dispatchEvent(new CustomEvent('openCanvasTab', { detail: data.response }));
          }

          if (data.response.content.includes('test_case_open')) {
            onTestCaseOpen?.();
            window.dispatchEvent(new CustomEvent('openTestCaseTab', { detail: data.response }));
          }
        } else {
          throw new Error(data.error || '未知错误');
        }
      } catch (err) {
        console.error('Mario HTTP API error:', err);
        const errorMessage = err instanceof Error ? err.message : '发送消息失败';
        setError(errorMessage);

        // 添加错误消息
        const errorMsg = createMessage(`错误: ${errorMessage}`, 'assistant');
        setMessages((prev) => [...prev, errorMsg]);
      } finally {
        setIsLoading(false);
      }
    },
    [createMessage, agentId, sessionId, messages, onCanvasOpen, onTestCaseOpen]
  );

  /**
   * 清空消息列表
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    isConnected: true // HTTP 模式下始终认为已连接
  };
}
