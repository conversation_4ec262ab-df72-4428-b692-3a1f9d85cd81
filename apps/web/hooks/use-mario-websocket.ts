import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Mario WebSocket 消息类型定义
 */
interface MarioMessage {
  type: string;
  content?: string;
  data?: any;
  timestamp?: number;
}

/**
 * <PERSON> 聊天消息类型定义
 */
interface MarioChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  createdAt: string;
}

/**
 * Mario WebSocket Hook 的返回类型
 */
interface UseMarioWebSocketReturn {
  messages: MarioChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => void;
  clearMessages: () => void;
  isConnected: boolean;
}

/**
 * Mario WebSocket Hook 配置选项
 */
interface UseMarioWebSocketOptions {
  url?: string;
  agentId: string;
  sessionId: string;
  onCanvasOpen?: () => void;
  onTestCaseOpen?: () => void;
}

/**
 * Mario WebSocket Hook
 * 用于处理与 Mario Agent 的 WebSocket 连接和消息交互
 */
export function useMarioWebSocket({
  url = process.env.NEXT_PUBLIC_MARIO_WEBSOCKET_URL || 'ws://localhost:8080/ws',
  agentId,
  sessionId,
  onCanvasOpen,
  onTestCaseOpen
}: UseMarioWebSocketOptions): UseMarioWebSocketReturn {
  const [messages, setMessages] = useState<MarioChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null);
  const messageQueueRef = useRef<string[]>([]);
  const currentMessageRef = useRef<MarioChatMessage | null>(null);

  /**
   * 创建新的消息对象
   */
  const createMessage = useCallback((content: string, role: 'user' | 'assistant' = 'assistant'): MarioChatMessage => {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      role,
      createdAt: new Date().toISOString()
    };
  }, []);

  /**
   * 处理接收到的 WebSocket 消息
   */
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const marioMessage: MarioMessage = JSON.parse(event.data);

        switch (marioMessage.type) {
          case 'interrupt':
            // 中断当前消息处理
            setIsLoading(false);
            currentMessageRef.current = null;
            break;

          case 'reporter_node_chain_end':
            // 节点链结束，清除等待状态
            setIsLoading(false);
            break;

          case 'markdownor':
          case 'model_response':
            // 处理 Markdown 或模型响应
            if (marioMessage.content) {
              if (currentMessageRef.current) {
                // 更新当前消息内容（流式输出）
                currentMessageRef.current.content += marioMessage.content;
                setMessages((prev) => {
                  const newMessages = [...prev];
                  const lastIndex = newMessages.length - 1;
                  if (
                    lastIndex >= 0 &&
                    currentMessageRef.current &&
                    newMessages[lastIndex] &&
                    newMessages[lastIndex].id === currentMessageRef.current.id
                  ) {
                    newMessages[lastIndex] = { ...currentMessageRef.current };
                  }
                  return newMessages;
                });
              } else {
                // 创建新消息
                const newMessage = createMessage(marioMessage.content);
                currentMessageRef.current = newMessage;
                setMessages((prev) => [...prev, newMessage]);
              }
            }
            break;

          case 'canvas_open':
            // 触发画布打开事件
            onCanvasOpen?.();
            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('openCanvasTab', { detail: marioMessage.data }));
            break;

          case 'test_case_open':
            // 触发测试用例打开事件
            onTestCaseOpen?.();
            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('openTestCaseTab', { detail: marioMessage.data }));
            break;

          default:
            console.log('Unknown Mario message type:', marioMessage.type);
        }
      } catch (err) {
        console.error('Error parsing Mario WebSocket message:', err);
        setError('消息解析错误');
      }
    },
    [createMessage, onCanvasOpen, onTestCaseOpen]
  );

  /**
   * 建立 WebSocket 连接
   */
  const connect = useCallback(() => {
    try {
      const wsUrl = `${url}?agentId=${agentId}&sessionId=${sessionId}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Mario WebSocket connected');
        setIsConnected(true);
        setError(null);

        // 启动心跳
        heartbeatRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ type: 'ping' }));
          }
        }, 30000);

        // 发送队列中的消息
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift();
          if (queuedMessage && wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(queuedMessage);
          }
        }
      };

      wsRef.current.onmessage = handleMessage;

      wsRef.current.onerror = (error) => {
        console.error('Mario WebSocket error:', error);
        setError('WebSocket 连接错误');
        setIsConnected(false);
      };

      wsRef.current.onclose = () => {
        console.log('Mario WebSocket disconnected');
        setIsConnected(false);

        // 清理心跳
        if (heartbeatRef.current) {
          clearInterval(heartbeatRef.current);
          heartbeatRef.current = null;
        }

        // 尝试重连（5秒后）
        setTimeout(() => {
          if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
            connect();
          }
        }, 5000);
      };
    } catch (err) {
      console.error('Error creating Mario WebSocket connection:', err);
      setError('连接创建失败');
    }
  }, [url, agentId, sessionId, handleMessage]);

  /**
   * 发送消息到 Mario Agent
   */
  const sendMessage = useCallback(
    (content: string) => {
      if (!content.trim()) return;

      // 添加用户消息到消息列表
      const userMessage = createMessage(content, 'user');
      setMessages((prev) => [...prev, userMessage]);

      // 设置加载状态
      setIsLoading(true);
      setError(null);

      // 准备发送的消息
      const messageToSend = JSON.stringify({
        type: 'user_message',
        content,
        agentId,
        sessionId,
        timestamp: Date.now()
      });

      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(messageToSend);
      } else {
        // 如果连接未就绪，将消息加入队列
        messageQueueRef.current.push(messageToSend);
        if (!isConnected) {
          connect();
        }
      }
    },
    [createMessage, agentId, sessionId, isConnected, connect]
  );

  /**
   * 清空消息列表
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    currentMessageRef.current = null;
    setError(null);
  }, []);

  /**
   * 组件挂载时建立连接
   */
  useEffect(() => {
    connect();

    return () => {
      // 清理资源
      if (heartbeatRef.current) {
        clearInterval(heartbeatRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connect]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    isConnected
  };
}
